#!/usr/bin/env python3
"""
Historical Cryptocurrency Data Collection Script
Fetches 3 years of historical price data for HNT comparison analysis
"""

import requests
import pandas as pd
import time
from datetime import datetime, timedelta
import json
import os

# CoinGecko API endpoint
BASE_URL = "https://api.coingecko.com/api/v3"

# Token mapping to CoinGecko IDs
TOKEN_IDS = {
    'BTC': 'bitcoin',
    'ETH': 'ethereum', 
    'SOL': 'solana',
    'MATIC': 'matic-network',
    'AVAX': 'avalanche-2',
    'HNT': 'helium',
    'FIL': 'filecoin',
    'RNDR': 'render-token',
    'TAO': 'bittensor',
    'AKT': 'akash-network',
    'IOTX': 'iotex',
    'THETA': 'theta-token'
}

def get_historical_data(coin_id, days=365):  # Start with 1 year to avoid rate limits
    """
    Fetch historical price data from CoinGecko API with better error handling
    """
    url = f"{BASE_URL}/coins/{coin_id}/market_chart"
    params = {
        'vs_currency': 'usd',
        'days': days,
        'interval': 'daily'
    }

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }

    try:
        response = requests.get(url, params=params, headers=headers, timeout=30)

        if response.status_code == 429:
            print(f"Rate limited for {coin_id}, waiting 60 seconds...")
            time.sleep(60)
            response = requests.get(url, params=params, headers=headers, timeout=30)

        if response.status_code == 401:
            print(f"Authentication required for {coin_id}, trying alternative approach...")
            # Try with shorter timeframe
            params['days'] = 90  # 3 months
            response = requests.get(url, params=params, headers=headers, timeout=30)

        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"Error fetching data for {coin_id}: {e}")
        return None

def process_price_data(data, symbol):
    """
    Convert CoinGecko API response to DataFrame
    """
    if not data or 'prices' not in data:
        return None
    
    prices = data['prices']
    market_caps = data.get('market_caps', [])
    volumes = data.get('total_volumes', [])
    
    df_data = []
    for i, price_point in enumerate(prices):
        timestamp, price = price_point
        date = datetime.fromtimestamp(timestamp / 1000).strftime('%Y-%m-%d')
        
        row = {
            'date': date,
            'symbol': symbol,
            'price_usd': price,
            'market_cap': market_caps[i][1] if i < len(market_caps) else None,
            'volume_24h': volumes[i][1] if i < len(volumes) else None
        }
        df_data.append(row)
    
    return pd.DataFrame(df_data)

def main():
    """
    Main function to collect all historical data
    """
    print("Starting historical data collection...")
    print(f"Collecting 3 years of data for {len(TOKEN_IDS)} tokens")
    
    all_data = []
    
    for symbol, coin_id in TOKEN_IDS.items():
        print(f"Fetching data for {symbol} ({coin_id})...")
        
        # Fetch data
        raw_data = get_historical_data(coin_id)
        
        if raw_data:
            # Process data
            df = process_price_data(raw_data, symbol)
            if df is not None:
                all_data.append(df)
                print(f"✓ Successfully collected {len(df)} days of data for {symbol}")
            else:
                print(f"✗ Failed to process data for {symbol}")
        else:
            print(f"✗ Failed to fetch data for {symbol}")
        
        # Rate limiting - be more conservative
        time.sleep(10)
    
    if all_data:
        # Combine all data
        combined_df = pd.concat(all_data, ignore_index=True)
        
        # Sort by date and symbol
        combined_df = combined_df.sort_values(['date', 'symbol'])
        
        # Save to CSV
        output_file = 'historical_price_data.csv'
        combined_df.to_csv(output_file, index=False)
        print(f"\n✓ Historical data saved to {output_file}")
        print(f"Total records: {len(combined_df)}")
        print(f"Date range: {combined_df['date'].min()} to {combined_df['date'].max()}")
        
        # Save summary statistics
        summary = combined_df.groupby('symbol').agg({
            'price_usd': ['min', 'max', 'mean'],
            'date': ['min', 'max'],
            'market_cap': 'mean',
            'volume_24h': 'mean'
        }).round(2)
        
        summary.to_csv('price_summary_stats.csv')
        print(f"✓ Summary statistics saved to price_summary_stats.csv")
        
    else:
        print("No data collected!")

if __name__ == "__main__":
    main()
