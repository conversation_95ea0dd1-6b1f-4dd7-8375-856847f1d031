#!/usr/bin/env python3
"""
HNT Comparison Analysis Framework
Comprehensive analysis comparing HNT performance to other cryptocurrencies
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Set up plotting style
plt.style.use('default')
sns.set_palette("husl")

class HNTAnalyzer:
    def __init__(self, data_file='historical_price_data.csv', tokens_file='tokens_dataset.csv'):
        """Initialize the analyzer with price and token metadata"""
        self.price_data = pd.read_csv(data_file)
        self.token_metadata = pd.read_csv(tokens_file)
        self.price_data['date'] = pd.to_datetime(self.price_data['date'])
        
        # Create pivot table for easier analysis
        self.price_pivot = self.price_data.pivot(index='date', columns='symbol', values='price_usd')
        
        print(f"Loaded data for {len(self.price_pivot.columns)} tokens")
        print(f"Date range: {self.price_pivot.index.min().date()} to {self.price_pivot.index.max().date()}")
    
    def calculate_returns(self, period='daily'):
        """Calculate returns for all tokens"""
        if period == 'daily':
            returns = self.price_pivot.pct_change().dropna()
        elif period == 'weekly':
            weekly_prices = self.price_pivot.resample('W').last()
            returns = weekly_prices.pct_change().dropna()
        elif period == 'monthly':
            monthly_prices = self.price_pivot.resample('M').last()
            returns = monthly_prices.pct_change().dropna()
        
        return returns
    
    def calculate_performance_metrics(self):
        """Calculate key performance metrics for all tokens"""
        returns = self.calculate_returns()
        
        metrics = {}
        for token in self.price_pivot.columns:
            if token in returns.columns:
                token_returns = returns[token].dropna()
                
                # Calculate metrics
                total_return = (self.price_pivot[token].iloc[-1] / self.price_pivot[token].iloc[0] - 1) * 100
                annualized_return = ((1 + token_returns.mean()) ** 252 - 1) * 100
                volatility = token_returns.std() * np.sqrt(252) * 100
                sharpe_ratio = annualized_return / volatility if volatility > 0 else 0
                max_drawdown = self.calculate_max_drawdown(token)
                
                metrics[token] = {
                    'Total Return (%)': round(total_return, 2),
                    'Annualized Return (%)': round(annualized_return, 2),
                    'Volatility (%)': round(volatility, 2),
                    'Sharpe Ratio': round(sharpe_ratio, 3),
                    'Max Drawdown (%)': round(max_drawdown, 2),
                    'Current Price': round(self.price_pivot[token].iloc[-1], 4)
                }
        
        return pd.DataFrame(metrics).T
    
    def calculate_max_drawdown(self, token):
        """Calculate maximum drawdown for a token"""
        prices = self.price_pivot[token].dropna()
        cumulative = (1 + prices.pct_change()).cumprod()
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max
        return drawdown.min() * 100
    
    def hnt_vs_category_comparison(self):
        """Compare HNT performance against different categories"""
        metrics = self.calculate_performance_metrics()
        
        # Categorize tokens
        major_crypto = ['BTC', 'ETH', 'SOL', 'MATIC', 'AVAX']
        depin_tokens = ['HNT', 'FIL', 'RNDR', 'TAO', 'AKT', 'IOTX', 'THETA']
        
        print("=== HNT vs Major Cryptocurrencies ===")
        major_metrics = metrics.loc[major_crypto]
        hnt_metrics = metrics.loc['HNT']
        
        print(f"HNT Total Return: {hnt_metrics['Total Return (%)']}%")
        print(f"Major Crypto Average: {major_metrics['Total Return (%)'].mean():.2f}%")
        print(f"HNT Rank vs Major Crypto: {(major_metrics['Total Return (%)'] < hnt_metrics['Total Return (%)']).sum() + 1}/6")
        
        print("\n=== HNT vs DePIN Competitors ===")
        depin_metrics = metrics.loc[depin_tokens]
        print(f"HNT Total Return: {hnt_metrics['Total Return (%)']}%")
        print(f"DePIN Average: {depin_metrics['Total Return (%)'].mean():.2f}%")
        print(f"HNT Rank vs DePIN: {(depin_metrics['Total Return (%)'] < hnt_metrics['Total Return (%)']).sum() + 1}/7")
        
        return metrics
    
    def correlation_analysis(self):
        """Analyze correlations between HNT and other tokens"""
        returns = self.calculate_returns()
        correlations = returns.corr()['HNT'].sort_values(ascending=False)
        
        print("=== HNT Correlation Analysis ===")
        print("Most correlated tokens with HNT:")
        for token, corr in correlations.head(6).items():
            if token != 'HNT':
                print(f"{token}: {corr:.3f}")
        
        return correlations
    
    def generate_summary_report(self):
        """Generate a comprehensive summary report"""
        print("=" * 60)
        print("HNT COMPREHENSIVE ANALYSIS REPORT")
        print("=" * 60)
        
        # Performance metrics
        metrics = self.calculate_performance_metrics()
        print("\n📊 PERFORMANCE METRICS (3-Year Period)")
        print("-" * 40)
        print(metrics.round(3))
        
        # Category comparisons
        print("\n🏆 CATEGORY COMPARISONS")
        print("-" * 40)
        self.hnt_vs_category_comparison()
        
        # Correlation analysis
        print("\n🔗 CORRELATION ANALYSIS")
        print("-" * 40)
        correlations = self.correlation_analysis()
        
        # Key insights
        hnt_metrics = metrics.loc['HNT']
        print(f"\n💡 KEY INSIGHTS FOR HNT")
        print("-" * 40)
        print(f"• Total 3-year return: {hnt_metrics['Total Return (%)']}%")
        print(f"• Annualized return: {hnt_metrics['Annualized Return (%)']}%")
        print(f"• Risk (volatility): {hnt_metrics['Volatility (%)']}%")
        print(f"• Risk-adjusted return (Sharpe): {hnt_metrics['Sharpe Ratio']}")
        print(f"• Worst drawdown: {hnt_metrics['Max Drawdown (%)']}%")
        print(f"• Current price: ${hnt_metrics['Current Price']}")
        
        return metrics

def main():
    """Run the complete HNT analysis"""
    analyzer = HNTAnalyzer()
    
    # Generate comprehensive report
    metrics = analyzer.generate_summary_report()
    
    # Save results
    metrics.to_csv('hnt_performance_analysis.csv')
    print(f"\n✅ Analysis complete! Results saved to 'hnt_performance_analysis.csv'")

if __name__ == "__main__":
    main()
